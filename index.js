import { Client, GatewayIntentBits, Collection } from 'discord.js';
import { config } from 'dotenv';
import { connectDatabase } from './src/database/connection.js';
import { loadCommands } from './src/utils/commandLoader.js';
import { loadEvents } from './src/utils/eventLoader.js';
import { logger } from './src/utils/logger.js';

// Carrega variáveis de ambiente
config();

// Cria uma nova instância do cliente Discord
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
    ]
});

// Cria uma coleção para armazenar comandos
client.commands = new Collection();

// Função principal para inicializar o bot
async function initializeBot() {
    try {
        logger.info('🚀 Iniciando o bot...');

        // Conecta ao banco de dados
        await connectDatabase();

        // Carrega comandos e eventos
        await loadCommands(client);
        await loadEvents(client);

        // Faz login no Discord
        await client.login(process.env.DISCORD_TOKEN);

    } catch (error) {
        logger.error('❌ Erro ao inicializar o bot:', error);
        process.exit(1);
    }
}

// Tratamento de erros não capturados
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

// Inicializa o bot
initializeBot();
