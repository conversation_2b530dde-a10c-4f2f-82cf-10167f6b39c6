import mongoose from 'mongoose';

const botConfigSchema = new mongoose.Schema({
    // Identificação do servidor
    guildId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    
    // Canais de logs
    adminLogChannelId: {
        type: String,
        default: null
    },
    publicLogChannelId: {
        type: String,
        default: null
    },
    
    // Configurações do MercadoPago
    mercadoPago: {
        accessToken: {
            type: String,
            default: null
        },
        publicKey: {
            type: String,
            default: null
        },
        webhookSecret: {
            type: String,
            default: null
        },
        isEnabled: {
            type: Boolean,
            default: false
        }
    },
    
    // Rate Limiting
    rateLimiting: {
        windowMs: {
            type: Number,
            default: 60000 // 1 minuto
        },
        maxRequests: {
            type: Number,
            default: 10
        },
        isEnabled: {
            type: Boolean,
            default: true
        }
    },
    
    // Metadados
    lastModifiedBy: {
        type: String // Discord ID do admin que modificou
    }
}, {
    timestamps: true,
    collection: 'bot_configs'
});

// Índices para otimização
botConfigSchema.index({ guildId: 1 });

// Métodos estáticos
botConfigSchema.statics.findByGuild = function(guildId) {
    return this.findOne({ guildId });
};

botConfigSchema.statics.createOrUpdate = async function(guildId, updates, modifiedBy) {
    const config = await this.findOneAndUpdate(
        { guildId },
        { 
            ...updates,
            lastModifiedBy: modifiedBy,
            updatedAt: new Date()
        },
        { 
            upsert: true, 
            new: true,
            setDefaultsOnInsert: true
        }
    );
    return config;
};

// Métodos de instância
botConfigSchema.methods.updateAdminLogChannel = function(channelId, modifiedBy) {
    this.adminLogChannelId = channelId;
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

botConfigSchema.methods.updatePublicLogChannel = function(channelId, modifiedBy) {
    this.publicLogChannelId = channelId;
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

botConfigSchema.methods.updateMercadoPago = function(mercadoPagoConfig, modifiedBy) {
    this.mercadoPago = { ...this.mercadoPago, ...mercadoPagoConfig };
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

botConfigSchema.methods.updateRateLimiting = function(rateLimitConfig, modifiedBy) {
    this.rateLimiting = { ...this.rateLimiting, ...rateLimitConfig };
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

export default mongoose.model('BotConfig', botConfigSchema);