import mongoose from 'mongoose';
import { logger } from '../utils/logger.js';

/**
 * Conecta ao banco de dados MongoDB
 */
export async function connectDatabase() {
    try {
        const mongoUri = process.env.MONGODB_URI;
        
        if (!mongoUri) {
            throw new Error('MONGODB_URI não está definida nas variáveis de ambiente');
        }

        // Configurações de conexão
        const options = {
            maxPoolSize: 10, // Máximo de 10 conexões simultâneas
            serverSelectionTimeoutMS: 5000, // Timeout de 5 segundos
            socketTimeoutMS: 45000, // Timeout de socket de 45 segundos
        };

        await mongoose.connect(mongoUri, options);
        
        logger.info('✅ Conectado ao MongoDB com sucesso');

        // Event listeners para monitorar a conexão
        mongoose.connection.on('error', (error) => {
            logger.error('❌ Erro na conexão MongoDB:', error);
        });

        mongoose.connection.on('disconnected', () => {
            logger.warn('⚠️ MongoDB desconectado');
        });

        mongoose.connection.on('reconnected', () => {
            logger.info('🔄 MongoDB reconectado');
        });

    } catch (error) {
        logger.error('❌ Erro ao conectar ao MongoDB:', error);
        throw error;
    }
}

/**
 * Desconecta do banco de dados
 */
export async function disconnectDatabase() {
    try {
        await mongoose.disconnect();
        logger.info('✅ Desconectado do MongoDB');
    } catch (error) {
        logger.error('❌ Erro ao desconectar do MongoDB:', error);
        throw error;
    }
}
