import { ChannelType, PermissionFlagsBits, EmbedBuilder, StringSelectMenuBuilder, ActionRowBuilder } from 'discord.js';
import Store from '../models/Store.js';
import Product from '../models/Product.js';
import { logger } from './logger.js';
import { BotLogger } from './botLogger.js';
import { BOT_CONFIG } from '../config/constants.js';

/**
 * Gerenciador de lojas
 */
export class StoreManager {
    /**
     * Cria uma nova loja
     * @param {ModalSubmitInteraction} interaction 
     * @param {Object} storeData 
     */
    static async createStore(interaction, storeData) {
        try {
            const { banner, name, color, description } = storeData;
            const guild = interaction.guild;
            const userId = interaction.user.id;

            // Validações básicas
            if (!this.isValidUrl(banner)) {
                throw new Error('URL do banner inválida');
            }

            if (!this.isValidColor(color)) {
                throw new Error('Cor inválida. Use formato hex (#FFFFFF) ou nome de cor');
            }

            // Verifica se já existe uma loja com o mesmo nome no servidor
            const existingStore = await Store.findOne({ 
                guildId: guild.id, 
                name: name.toLowerCase(),
                isActive: true 
            });

            if (existingStore) {
                throw new Error('Já existe uma loja com este nome no servidor');
            }

            // Cria o canal da loja
            const channel = await guild.channels.create({
                name: name.toLowerCase().replace(/\s+/g, '-'),
                type: ChannelType.GuildText,
                topic: `Loja: ${name} - ${description.substring(0, 100)}`,
                permissionOverwrites: [
                    {
                        id: guild.roles.everyone.id,
                        deny: [PermissionFlagsBits.ViewChannel]
                    },
                    {
                        id: guild.members.me.id,
                        allow: [
                            PermissionFlagsBits.ViewChannel,
                            PermissionFlagsBits.SendMessages,
                            PermissionFlagsBits.EmbedLinks,
                            PermissionFlagsBits.ManageMessages
                        ]
                    }
                ]
            });

            // Adiciona permissões para administradores
            const adminRoles = guild.roles.cache.filter(role => 
                role.permissions.has(PermissionFlagsBits.Administrator) && 
                role.id !== guild.roles.everyone.id
            );

            for (const role of adminRoles.values()) {
                await channel.permissionOverwrites.create(role.id, {
                    ViewChannel: true,
                    SendMessages: true,
                    ReadMessageHistory: true
                });
            }

            // Cria o embed da loja
            const embed = await this.createStoreEmbed(name, description, banner, color, guild.id);
            const selectMenu = await this.createProductSelectMenu(guild.id);
            const row = new ActionRowBuilder().addComponents(selectMenu);

            // Envia a mensagem no canal
            const message = await channel.send({
                embeds: [embed],
                components: [row]
            });

            // Salva a loja no banco de dados
            const store = new Store({
                name,
                description,
                banner,
                color,
                guildId: guild.id,
                channelId: channel.id,
                messageId: message.id,
                createdBy: userId
            });

            await store.save();

            logger.info(`Loja "${name}" criada com sucesso por ${interaction.user.tag} em ${guild.name}`);

            // Log da criação da loja
            await BotLogger.logStoreCreated(guild, interaction.user, store, channel);

            return {
                success: true,
                store,
                channel,
                message
            };

        } catch (error) {
            logger.error('Erro ao criar loja:', error);
            throw error;
        }
    }

    /**
     * Cria o embed da loja
     */
    static async createStoreEmbed(name, description, banner, color, guildId) {
        const embed = new EmbedBuilder()
            .setTitle(`🏪 ${name}`)
            .setDescription(description)
            .setImage(banner)
            .setColor(this.parseColor(color))
            .setTimestamp()
            .setFooter({ 
                text: 'Selecione um produto no menu abaixo para ver mais detalhes' 
            });

        return embed;
    }

    /**
     * Reenvia a mensagem de uma loja (apaga a antiga e cria nova)
     * @param {Guild} guild 
     * @param {Store} store 
     */
    static async resendStoreMessage(guild, store) {
        try {
            // Busca o canal da loja
            const channel = guild.channels.cache.get(store.channelId);
            if (!channel) {
                throw new Error('Canal da loja não encontrado');
            }

            // Tenta apagar a mensagem antiga se existir
            if (store.messageId) {
                try {
                    const oldMessage = await channel.messages.fetch(store.messageId);
                    if (oldMessage) {
                        await oldMessage.delete();
                    }
                } catch (error) {
                    logger.warn(`Não foi possível apagar mensagem antiga da loja "${store.name}": ${error.message}`);
                }
            }

            // Cria novo embed e select menu
            const embed = await this.createStoreEmbed(store.name, store.description, store.banner, store.color, guild.id);
            const selectMenu = await this.createProductSelectMenu(guild.id);
            const row = new ActionRowBuilder().addComponents(selectMenu);

            // Envia nova mensagem
            const newMessage = await channel.send({
                embeds: [embed],
                components: [row]
            });

            // Atualiza o messageId no banco de dados
            store.messageId = newMessage.id;
            await store.save();

            logger.info(`Mensagem da loja "${store.name}" reenviada com sucesso`);

            return {
                success: true,
                message: newMessage
            };

        } catch (error) {
            logger.error(`Erro ao reenviar mensagem da loja "${store.name}":`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Cria o select menu de produtos
     */
    static async createProductSelectMenu(guildId) {
        try {
            // Busca produtos ativos
            const products = await Product.find({ 
                status: 'active',
                stock: { $gt: 0 }
            }).limit(20);

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('store_product_select');

            if (products.length === 0) {
                // Quando não há produtos, desabilita o dropdown com mensagem apropriada
                selectMenu
                    .setPlaceholder('Não há produtos')
                    .setDisabled(true)
                    .addOptions({
                        label: 'Nenhum produto',
                        description: 'Não há produtos disponíveis',
                        value: 'disabled',
                        emoji: '📭'
                    });
            } else {
                // Quando há produtos, habilita o dropdown
                selectMenu.setPlaceholder('Selecione um produto...');
                
                // Adiciona produtos ao menu
                products.forEach(product => {
                    selectMenu.addOptions({
                        label: product.name.substring(0, 100),
                        description: product.description.substring(0, 100),
                        value: product._id.toString(),
                        emoji: product.isDigital ? '💾' : '📦'
                    });
                });
                
                // Adiciona opção de cancelar apenas quando há produtos
                selectMenu.addOptions({
                    label: 'Cancelar seleção',
                    description: 'Fechar o menu de produtos',
                    value: 'cancel',
                    emoji: '❌'
                });
            }

            return selectMenu;

        } catch (error) {
            logger.error('Erro ao criar select menu de produtos:', error);
            
            // Menu de fallback em caso de erro
            return new StringSelectMenuBuilder()
                .setCustomId('store_product_select')
                .setPlaceholder('Erro ao carregar produtos')
                .setDisabled(true)
                .addOptions({
                    label: 'Erro ao carregar',
                    description: 'Tente novamente mais tarde',
                    value: 'error',
                    emoji: '❌'
                });
        }
    }

    /**
     * Valida se uma URL é válida
     */
    static isValidUrl(url) {
        try {
            new URL(url);
            return /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(url);
        } catch {
            return false;
        }
    }

    /**
     * Valida se uma cor é válida
     */
    static isValidColor(color) {
        return /^#[0-9A-F]{6}$/i.test(color) || /^[a-zA-Z]+$/i.test(color);
    }

    /**
     * Converte cor para formato Discord.js
     */
    static parseColor(color) {
        const colorMap = {
            'red': '#FF0000',
            'green': '#00FF00',
            'blue': '#0000FF',
            'yellow': '#FFFF00',
            'purple': '#800080',
            'orange': '#FFA500',
            'pink': '#FFC0CB',
            'black': '#000000',
            'white': '#FFFFFF',
            'gray': '#808080',
            'grey': '#808080'
        };

        if (color.startsWith('#')) {
            return parseInt(color.replace('#', ''), 16);
        }

        const lowerColor = color.toLowerCase();
        if (colorMap[lowerColor]) {
            return parseInt(colorMap[lowerColor].replace('#', ''), 16);
        }

        return parseInt('0099ff', 16); // Cor padrão
    }

    /**
     * Atualiza o embed da loja no canal
     */
    static async updateStoreEmbed(store, guild) {
        try {
            // Busca o canal da loja
            const channel = guild.channels.cache.get(store.channelId);
            if (!channel) {
                logger.warn(`Canal da loja ${store.name} não encontrado: ${store.channelId}`);
                return false;
            }

            // Busca a mensagem do embed
            let message = null;
            if (store.messageId) {
                try {
                    message = await channel.messages.fetch(store.messageId);
                } catch (error) {
                    logger.warn(`Mensagem do embed da loja ${store.name} não encontrada: ${store.messageId}`);
                }
            }

            // Cria o novo embed
            const embed = await this.createStoreEmbed(
                store.name,
                store.description,
                store.banner,
                store.color,
                guild.id
            );

            // Cria o novo select menu
            const selectMenu = await this.createProductSelectMenu(guild.id);
            const row = new ActionRowBuilder().addComponents(selectMenu);

            if (message) {
                // Atualiza a mensagem existente
                await message.edit({
                    embeds: [embed],
                    components: [row]
                });
                logger.info(`Embed da loja "${store.name}" atualizado com sucesso`);
            } else {
                // Cria uma nova mensagem se a original não foi encontrada
                const newMessage = await channel.send({
                    embeds: [embed],
                    components: [row]
                });

                // Atualiza o messageId no banco de dados
                await Store.findByIdAndUpdate(store._id, { messageId: newMessage.id });
                logger.info(`Nova mensagem criada para a loja "${store.name}"`);
            }

            return true;

        } catch (error) {
            logger.error(`Erro ao atualizar embed da loja ${store.name}:`, error);
            return false;
        }
    }

    /**
     * Deleta uma loja completamente
     * @param {Guild} guild
     * @param {Store} store
     * @param {User} deletedBy
     */
    static async deleteStore(guild, store, deletedBy) {
        try {
            logger.info(`Iniciando deleção da loja "${store.name}" por ${deletedBy.tag}`);

            // 1. Tenta deletar o canal da loja
            try {
                const channel = guild.channels.cache.get(store.channelId);
                if (channel) {
                    await channel.delete(`Loja "${store.name}" deletada por ${deletedBy.tag}`);
                    logger.info(`Canal da loja "${store.name}" deletado com sucesso`);
                } else {
                    logger.warn(`Canal da loja "${store.name}" não encontrado (ID: ${store.channelId})`);
                }
            } catch (channelError) {
                logger.error(`Erro ao deletar canal da loja "${store.name}":`, channelError);
                // Continua mesmo se não conseguir deletar o canal
            }

            // 2. Marca a loja como inativa no banco de dados
            await Store.findByIdAndUpdate(store._id, {
                isActive: false,
                lastModifiedBy: deletedBy.id
            });

            // 3. Remove completamente a loja do banco de dados
            await Store.findByIdAndDelete(store._id);

            logger.info(`Loja "${store.name}" deletada com sucesso do banco de dados`);

            // 4. Log da deleção da loja
            await BotLogger.logStoreDeleted(guild, deletedBy, store);

            return {
                success: true,
                message: `Loja **${store.name}** deletada com sucesso!`,
                deletedStore: {
                    name: store.name,
                    channelId: store.channelId,
                    id: store._id
                }
            };

        } catch (error) {
            logger.error(`Erro ao deletar loja "${store.name}":`, error);

            return {
                success: false,
                message: `Erro ao deletar a loja **${store.name}**. Tente novamente mais tarde.`,
                error: error.message
            };
        }
    }
}
