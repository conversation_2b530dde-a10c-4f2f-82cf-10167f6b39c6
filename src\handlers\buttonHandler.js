import { logger } from '../utils/logger.js';
import { <PERSON><PERSON><PERSON><PERSON>er, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, ChannelSelectMenuBuilder, ChannelType, ButtonBuilder, ButtonStyle } from 'discord.js';
import BotConfig from '../models/BotConfig.js';
import { BotLogger } from '../utils/botLogger.js';
import { rateLimiter } from '../utils/rateLimiter.js';
import { COLORS, EMOJIS } from '../config/constants.js';

/**
 * Manipula interações de botões
 * @param {ButtonInteraction} interaction 
 */
export async function handleButton(interaction) {
    const customId = interaction.customId;
    
    try {
        logger.info(`Botão clicado: ${customId} por ${interaction.user.tag}`);

        // Roteamento baseado no customId do botão
        // Exemplo de estrutura: categoria_acao_id
        const [category, action, id] = customId.split('_');

        switch (category) {
            case 'store':
                await handleStoreButton(interaction, action, id);
                break;
            case 'admin':
                await handleAdminButton(interaction, action, id);
                break;
            case 'debug':
                await handleDebugButton(interaction, action, id);
                break;
            case 'delete':
                await handleDeleteButton(interaction, action, id);
                break;
            case 'config':
                await handleConfigButton(interaction, action, id);
                break;
            default:
                logger.warn(`Categoria de botão não reconhecida: ${category}`);
                await interaction.reply({
                    content: '❌ Botão não reconhecido.',
                    ephemeral: true
                });
        }

    } catch (error) {
        logger.error(`Erro ao processar botão ${customId}:`, error);
        
        const errorMessage = {
            content: '❌ Erro ao processar a ação do botão.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Manipula botões relacionados à loja
 */
async function handleStoreButton(interaction, action, id) {
    switch (action) {
        case 'buy':
            await interaction.reply({
                content: `🛒 Funcionalidade de compra será implementada em breve! (ID: ${id})`,
                ephemeral: true
            });
            break;
        case 'info':
            await interaction.reply({
                content: `ℹ️ Informações do produto serão exibidas aqui! (ID: ${id})`,
                ephemeral: true
            });
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de loja não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula botões administrativos
 */
async function handleAdminButton(interaction, action, id) {
    // Verificação básica de permissões (pode ser expandida)
    if (!interaction.member.permissions.has('ADMINISTRATOR')) {
        return await interaction.reply({
            content: '❌ Você não tem permissão para usar este botão.',
            ephemeral: true
        });
    }

    switch (action) {
        case 'approve':
            await interaction.reply({
                content: `✅ Item aprovado! (ID: ${id})`,
                ephemeral: true
            });
            break;
        case 'reject':
            await interaction.reply({
                content: `❌ Item rejeitado! (ID: ${id})`,
                ephemeral: true
            });
            break;
        default:
            await interaction.reply({
                content: '❌ Ação administrativa não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula botões de debug
 */
async function handleDebugButton(interaction, action, id) {
    // Verificação se o usuário é administrador
    if (!interaction.member.permissions.has('Administrator')) {
        return await interaction.reply({
            content: '❌ Apenas administradores podem usar ferramentas de debug.',
            ephemeral: true
        });
    }

    switch (action) {
        case 'stores':
            await handleStoresDebug(interaction);
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de debug não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula botões de deleção
 */
async function handleDeleteButton(interaction, action, id) {
    // Verificação se o usuário é administrador
    if (!interaction.member.permissions.has('Administrator')) {
        return await interaction.reply({
            content: '❌ Apenas administradores podem deletar lojas.',
            ephemeral: true
        });
    }

    switch (action) {
        case 'store':
            await handleDeleteStoreConfirm(interaction, id);
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de deleção não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Exibe informações de debug das lojas
 */
async function handleStoresDebug(interaction) {
    try {
        const Store = (await import('../models/Store.js')).default;

        // Busca todas as lojas do servidor
        const stores = await Store.find({ guildId: interaction.guild.id });

        if (stores.length === 0) {
            return await interaction.reply({
                content: '🐛 **Debug - Lojas**\n\n❌ Nenhuma loja encontrada neste servidor.',
                ephemeral: true
            });
        }

        let debugInfo = '🐛 **Debug - Informações das Lojas**\n\n';

        stores.forEach((store, index) => {
            debugInfo += `**${index + 1}. ${store.name}**\n`;
            debugInfo += `• ID: \`${store._id}\`\n`;
            debugInfo += `• Canal ID: \`${store.channelId}\`\n`;
            debugInfo += `• Mensagem ID: \`${store.messageId || 'N/A'}\`\n`;
            debugInfo += `• Ativa: ${store.isActive ? '✅' : '❌'}\n`;
            debugInfo += `• Criada: ${store.createdAt.toLocaleString('pt-BR')}\n`;
            debugInfo += `• Atualizada: ${store.updatedAt.toLocaleString('pt-BR')}\n`;
            debugInfo += `• Cor: \`${store.color}\`\n`;
            debugInfo += `• Banner: ${store.banner ? '✅' : '❌'}\n\n`;
        });

        // Limita o tamanho da mensagem
        if (debugInfo.length > 1900) {
            debugInfo = debugInfo.substring(0, 1900) + '\n\n... (truncado)';
        }

        await interaction.reply({
            content: debugInfo,
            ephemeral: true
        });

        logger.info(`Debug de lojas executado por ${interaction.user.tag} em ${interaction.guild.name}`);

    } catch (error) {
        logger.error('Erro ao executar debug de lojas:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar informações de debug das lojas.',
            ephemeral: true
        });
    }
}

/**
 * Confirma e executa a deleção de uma loja
 */
async function handleDeleteStoreConfirm(interaction, storeId) {
    try {
        // Defer a resposta pois a deleção pode demorar
        await interaction.deferReply({ ephemeral: true });

        const Store = (await import('../models/Store.js')).default;
        const { StoreManager } = await import('../utils/storeManager.js');

        // Busca a loja
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.editReply({
                content: '❌ Loja não encontrada ou já foi deletada.'
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Esta loja não pertence a este servidor.'
            });
        }

        // Executa a deleção
        const result = await StoreManager.deleteStore(interaction.guild, store, interaction.user);

        if (result.success) {
            await interaction.editReply({
                content: `✅ ${result.message}\n\n` +
                        `**Detalhes da loja deletada:**\n` +
                        `• Nome: ${result.deletedStore.name}\n` +
                        `• Canal ID: ${result.deletedStore.channelId}\n` +
                        `• ID da Loja: \`${result.deletedStore.id}\``
            });

            logger.info(`Loja "${result.deletedStore.name}" deletada com sucesso por ${interaction.user.tag} em ${interaction.guild.name}`);
        } else {
            await interaction.editReply({
                content: `❌ ${result.message}`
            });

            logger.error(`Falha ao deletar loja: ${result.error}`);
        }

    } catch (error) {
        logger.error('Erro ao confirmar deleção de loja:', error);

        const errorMessage = {
            content: '❌ Erro ao deletar a loja. Tente novamente mais tarde.'
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(errorMessage);
        } else {
            await interaction.reply({ ...errorMessage, ephemeral: true });
        }
    }
}

/**
 * Manipula botões de configuração do bot
 * @param {import('discord.js').ButtonInteraction} interaction 
 * @param {string} action 
 * @param {string} id 
 */
async function handleConfigButton(interaction, action, id) {
    try {
        // Verificação de permissão
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem usar esta configuração.',
                ephemeral: true
            });
        }

        switch (action) {
            case 'admin_logs':
                await handleAdminLogsConfig(interaction);
                break;
            case 'public_logs':
                await handlePublicLogsConfig(interaction);
                break;
            case 'mercadopago':
                await handleMercadoPagoConfig(interaction);
                break;
            case 'rate_limit':
                await handleRateLimitConfig(interaction);
                break;
            case 'view_logs':
                await handleViewLogs(interaction);
                break;
            case 'reset_all':
                await handleResetConfig(interaction);
                break;
            default:
                logger.warn(`Ação de configuração não reconhecida: ${action}`);
                await interaction.reply({
                    content: '❌ Configuração não reconhecida.',
                    ephemeral: true
                });
        }
    } catch (error) {
        logger.error('Erro no handler de configuração:', error);
        
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
                content: '❌ Ocorreu um erro ao processar a configuração.',
                ephemeral: true
            });
        }
    }
}

/**
 * Configura canal de logs administrativos
 */
async function handleAdminLogsConfig(interaction) {
    const embed = new EmbedBuilder()
        .setColor(COLORS.PRIMARY)
        .setTitle(`${EMOJIS.INFO} Configurar Canal de Logs Admin`)
        .setDescription('Selecione o canal onde serão enviados os logs administrativos do bot.');

    const selectMenu = new ChannelSelectMenuBuilder()
        .setCustomId('config_select_admin_channel')
        .setPlaceholder('Selecione um canal de texto')
        .setChannelTypes(ChannelType.GuildText);

    const row = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.reply({
        embeds: [embed],
        components: [row],
        ephemeral: true
    });
}

/**
 * Configura canal de logs públicas
 */
async function handlePublicLogsConfig(interaction) {
    const embed = new EmbedBuilder()
        .setColor(COLORS.PRIMARY)
        .setTitle(`${EMOJIS.INFO} Configurar Canal de Logs Públicas`)
        .setDescription('Selecione o canal onde serão enviados os logs públicos (vendas, etc.).');

    const selectMenu = new ChannelSelectMenuBuilder()
        .setCustomId('config_select_public_channel')
        .setPlaceholder('Selecione um canal de texto')
        .setChannelTypes(ChannelType.GuildText);

    const row = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.reply({
        embeds: [embed],
        components: [row],
        ephemeral: true
    });
}

/**
 * Configura API do MercadoPago
 */
async function handleMercadoPagoConfig(interaction) {
    const modal = new ModalBuilder()
        .setCustomId('config_mercadopago_modal')
        .setTitle('Configurar MercadoPago');

    const accessTokenInput = new TextInputBuilder()
        .setCustomId('mp_access_token')
        .setLabel('Access Token')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('APP_USR-...')
        .setRequired(true)
        .setMaxLength(200);

    const publicKeyInput = new TextInputBuilder()
        .setCustomId('mp_public_key')
        .setLabel('Public Key')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('APP_USR-...')
        .setRequired(true)
        .setMaxLength(200);

    const webhookSecretInput = new TextInputBuilder()
        .setCustomId('mp_webhook_secret')
        .setLabel('Webhook Secret (opcional)')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Deixe vazio se não usar webhooks')
        .setRequired(false)
        .setMaxLength(100);

    const row1 = new ActionRowBuilder().addComponents(accessTokenInput);
    const row2 = new ActionRowBuilder().addComponents(publicKeyInput);
    const row3 = new ActionRowBuilder().addComponents(webhookSecretInput);

    modal.addComponents(row1, row2, row3);

    await interaction.showModal(modal);
}

/**
 * Configura Rate Limiting
 */
async function handleRateLimitConfig(interaction) {
    const modal = new ModalBuilder()
        .setCustomId('config_rate_limit_modal')
        .setTitle('Configurar Rate Limiting');

    const maxRequestsInput = new TextInputBuilder()
        .setCustomId('rl_max_requests')
        .setLabel('Máximo de Requisições por Minuto')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('10')
        .setRequired(true)
        .setMaxLength(3);

    const windowMsInput = new TextInputBuilder()
        .setCustomId('rl_window_ms')
        .setLabel('Janela de Tempo (ms)')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('60000')
        .setRequired(true)
        .setMaxLength(10);

    const enabledInput = new TextInputBuilder()
        .setCustomId('rl_enabled')
        .setLabel('Ativado (true/false)')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('true')
        .setRequired(true)
        .setMaxLength(5);

    const row1 = new ActionRowBuilder().addComponents(maxRequestsInput);
    const row2 = new ActionRowBuilder().addComponents(windowMsInput);
    const row3 = new ActionRowBuilder().addComponents(enabledInput);

    modal.addComponents(row1, row2, row3);

    await interaction.showModal(modal);
}

/**
 * Visualiza logs de configuração
 */
async function handleViewLogs(interaction) {
    const config = await BotConfig.findByGuild(interaction.guild.id);
    
    const embed = new EmbedBuilder()
        .setColor(COLORS.INFO)
        .setTitle(`${EMOJIS.INFO} Logs de Configuração`)
        .setDescription('Histórico de configurações do bot neste servidor')
        .addFields(
            {
                name: '📊 Estatísticas de Rate Limit',
                value: config?.rateLimiting?.isEnabled 
                    ? `Ativo - ${config.rateLimiting.maxRequests} req/min`
                    : 'Desativado',
                inline: true
            },
            {
                name: '🔧 Última Configuração',
                value: config?.updatedAt 
                    ? `<t:${Math.floor(config.updatedAt.getTime() / 1000)}:F>`
                    : 'Nunca configurado',
                inline: true
            },
            {
                name: '👤 Configurado por',
                value: config?.lastModifiedBy 
                    ? `<@${config.lastModifiedBy}>`
                    : 'Sistema',
                inline: true
            }
        )
        .setTimestamp();

    await interaction.reply({
        embeds: [embed],
        ephemeral: true
    });
}

/**
 * Reset de todas as configurações
 */
async function handleResetConfig(interaction) {
    const embed = new EmbedBuilder()
        .setColor(COLORS.WARNING)
        .setTitle(`${EMOJIS.WARNING} Confirmar Reset`)
        .setDescription('⚠️ **ATENÇÃO**: Esta ação irá resetar TODAS as configurações do bot neste servidor.\n\nIsso inclui:\n• Canais de logs\n• Configurações do MercadoPago\n• Rate limiting\n• Todas as outras configurações\n\n**Esta ação não pode ser desfeita!**');

    const confirmButton = new ButtonBuilder()
        .setCustomId('config_confirm_reset')
        .setLabel('Confirmar Reset')
        .setEmoji('🔄')
        .setStyle(ButtonStyle.Danger);

    const cancelButton = new ButtonBuilder()
        .setCustomId('config_cancel_reset')
        .setLabel('Cancelar')
        .setEmoji('❌')
        .setStyle(ButtonStyle.Secondary);

    const row = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

    await interaction.reply({
        embeds: [embed],
        components: [row],
        ephemeral: true
    });
}
