{"name": "discord-store-bot", "version": "1.0.0", "description": "Bot Discord modular para loja com integração MongoDB", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "deploy-commands": "node scripts/deploy-commands.js"}, "keywords": ["discord", "bot", "store", "mongodb", "modular"], "author": "", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "mongoose": "^8.0.3", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.11.0"}}