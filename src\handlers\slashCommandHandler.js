import { logger } from '../utils/logger.js';
import { rateLimiter } from '../utils/rateLimiter.js';
import { BotLogger } from '../utils/botLogger.js';
import BotConfig from '../models/BotConfig.js';

/**
 * Manipula comandos slash
 * @param {ChatInputCommandInteraction} interaction 
 */
export async function handleSlashCommand(interaction) {
    const { commandName } = interaction;
    
    try {
        logger.info(`Comando /${commandName} executado por ${interaction.user.tag} em ${interaction.guild?.name || 'DM'}`);
        
        // Verifica rate limiting se estiver em um servidor
        if (interaction.guild) {
            const config = await BotConfig.findByGuild(interaction.guild.id);
            
            if (config?.rateLimiting?.isEnabled !== false) {
                const rateLimitConfig = {
                    windowMs: config?.rateLimiting?.windowMs || 60000,
                    maxRequests: config?.rateLimiting?.maxRequests || 10
                };
                
                const rateLimitResult = await rateLimiter.checkRateLimit(
                    interaction.user.id,
                    interaction.guild.id,
                    rateLimitConfig
                );
                
                if (rateLimitResult.isLimited) {
                    const warningMessage = rateLimiter.formatWarningMessage(rateLimitResult);
                    
                    await interaction.reply({
                        content: warningMessage,
                        ephemeral: true
                    });
                    
                    // Log do rate limit
                    await BotLogger.logRateLimit(
                        interaction.guild,
                        interaction.user,
                        commandName,
                        rateLimitResult
                    );
                    
                    return;
                }
            }
        }
        
        // Busca o comando na coleção de comandos do cliente
        const command = interaction.client.commands.get(commandName);
        
        if (!command) {
            logger.warn(`Comando não encontrado: ${commandName}`);
            await interaction.reply({
                content: '❌ Comando não encontrado.',
                ephemeral: true
            });
            return;
        }
        
        // Executa o comando
        await command.execute(interaction);
        
        logger.debug(`Comando /${commandName} executado com sucesso`);
        
    } catch (error) {
        logger.error(`Erro ao executar comando /${commandName}:`, error);
        
        const errorMessage = {
            content: '❌ Ocorreu um erro ao executar este comando.',
            ephemeral: true
        };
        
        // Verifica se a interação já foi respondida
        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}
