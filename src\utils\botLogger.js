import { EmbedBuilder } from 'discord.js';
import { logger } from './logger.js';
import BotConfig from '../models/BotConfig.js';
import { COLORS, EMOJIS } from '../config/constants.js';

/**
 * Sistema de logs do bot para canais específicos
 */
export class BotLogger {
    /**
     * Envia log para canal de administradores
     * @param {Client} client - Cliente do Discord
     * @param {string} guildId - ID do servidor
     * @param {Object} logData - Dados do log
     */
    static async logAdmin(client, guildId, logData) {
        try {
            const config = await BotConfig.findByGuild(guildId);
            if (!config || !config.adminLogChannelId) {
                return;
            }

            const channel = client.channels.cache.get(config.adminLogChannelId);
            if (!channel) {
                logger.warn(`Canal de log admin não encontrado: ${config.adminLogChannelId}`);
                return;
            }

            const embed = this.createAdminLogEmbed(logData);
            await channel.send({ embeds: [embed] });

        } catch (error) {
            logger.error('Erro ao enviar log admin:', error);
        }
    }

    /**
     * Envia log para canal público
     * @param {Client} client - Cliente do Discord
     * @param {string} guildId - ID do servidor
     * @param {Object} logData - Dados do log
     */
    static async logPublic(client, guildId, logData) {
        try {
            const config = await BotConfig.findByGuild(guildId);
            if (!config || !config.publicLogChannelId) {
                return;
            }

            const channel = client.channels.cache.get(config.publicLogChannelId);
            if (!channel) {
                logger.warn(`Canal de log público não encontrado: ${config.publicLogChannelId}`);
                return;
            }

            const embed = this.createPublicLogEmbed(logData);
            await channel.send({ embeds: [embed] });

        } catch (error) {
            logger.error('Erro ao enviar log público:', error);
        }
    }

    /**
     * Cria embed para logs administrativos
     * @param {Object} logData - Dados do log
     */
    static createAdminLogEmbed(logData) {
        const { action, user, details, timestamp = new Date() } = logData;
        
        const embed = new EmbedBuilder()
            .setColor(this.getColorByAction(action))
            .setTitle(`${this.getEmojiByAction(action)} ${action}`)
            .setTimestamp(timestamp)
            .setFooter({ text: 'Log Administrativo' });

        if (user) {
            embed.addFields({
                name: '👤 Usuário',
                value: `<@${user.id}> (${user.tag})`,
                inline: true
            });
        }

        if (details) {
            if (typeof details === 'string') {
                embed.setDescription(details);
            } else {
                Object.entries(details).forEach(([key, value]) => {
                    embed.addFields({
                        name: key,
                        value: String(value),
                        inline: true
                    });
                });
            }
        }

        return embed;
    }

    /**
     * Cria embed para logs públicos
     * @param {Object} logData - Dados do log
     */
    static createPublicLogEmbed(logData) {
        const { action, user, details, timestamp = new Date() } = logData;
        
        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${EMOJIS.SUCCESS} ${action}`)
            .setTimestamp(timestamp);

        if (user && action.includes('Venda')) {
            embed.addFields({
                name: '🛒 Cliente',
                value: user.username || 'Cliente',
                inline: true
            });
        }

        if (details) {
            if (details.product) {
                embed.addFields({
                    name: '📦 Produto',
                    value: details.product,
                    inline: true
                });
            }
            if (details.amount) {
                embed.addFields({
                    name: '💰 Valor',
                    value: `R$ ${details.amount.toFixed(2)}`,
                    inline: true
                });
            }
            if (details.quantity) {
                embed.addFields({
                    name: '📊 Quantidade',
                    value: String(details.quantity),
                    inline: true
                });
            }
        }

        return embed;
    }

    /**
     * Retorna cor baseada na ação
     * @param {string} action - Ação realizada
     */
    static getColorByAction(action) {
        const actionLower = action.toLowerCase();
        
        if (actionLower.includes('erro') || actionLower.includes('falha')) {
            return COLORS.ERROR;
        }
        if (actionLower.includes('aviso') || actionLower.includes('warning')) {
            return COLORS.WARNING;
        }
        if (actionLower.includes('sucesso') || actionLower.includes('criado') || actionLower.includes('venda')) {
            return COLORS.SUCCESS;
        }
        if (actionLower.includes('deletado') || actionLower.includes('removido')) {
            return COLORS.ERROR;
        }
        if (actionLower.includes('editado') || actionLower.includes('modificado')) {
            return COLORS.WARNING;
        }
        
        return COLORS.INFO;
    }

    /**
     * Retorna emoji baseado na ação
     * @param {string} action - Ação realizada
     */
    static getEmojiByAction(action) {
        const actionLower = action.toLowerCase();
        
        if (actionLower.includes('erro') || actionLower.includes('falha')) {
            return EMOJIS.ERROR;
        }
        if (actionLower.includes('aviso') || actionLower.includes('warning')) {
            return EMOJIS.WARNING;
        }
        if (actionLower.includes('sucesso') || actionLower.includes('criado') || actionLower.includes('venda')) {
            return EMOJIS.SUCCESS;
        }
        if (actionLower.includes('deletado') || actionLower.includes('removido')) {
            return '🗑️';
        }
        if (actionLower.includes('editado') || actionLower.includes('modificado')) {
            return '✏️';
        }
        if (actionLower.includes('loja')) {
            return '🏪';
        }
        if (actionLower.includes('produto')) {
            return '📦';
        }
        
        return EMOJIS.INFO;
    }

    /**
     * Logs específicos para diferentes ações
     */
    static async logStoreCreated(client, guildId, user, storeName, channelId) {
        await this.logAdmin(client, guildId, {
            action: 'Loja Criada',
            user,
            details: {
                '🏪 Nome da Loja': storeName,
                '📍 Canal': `<#${channelId}>`,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    static async logStoreDeleted(client, guildId, user, storeName) {
        await this.logAdmin(client, guildId, {
            action: 'Loja Deletada',
            user,
            details: {
                '🏪 Nome da Loja': storeName,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    static async logProductCreated(client, guildId, user, productName, storeName) {
        await this.logAdmin(client, guildId, {
            action: 'Produto Criado',
            user,
            details: {
                '📦 Produto': productName,
                '🏪 Loja': storeName,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    static async logSaleCompleted(client, guildId, user, productName, amount, quantity) {
        // Log público
        await this.logPublic(client, guildId, {
            action: 'Venda Realizada',
            user,
            details: {
                product: productName,
                amount,
                quantity
            }
        });

        // Log admin
        await this.logAdmin(client, guildId, {
            action: 'Venda Concluída',
            user,
            details: {
                '📦 Produto': productName,
                '💰 Valor': `R$ ${amount.toFixed(2)}`,
                '📊 Quantidade': quantity,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    static async logConfigChanged(client, guildId, user, configType, newValue) {
        await this.logAdmin(client, guildId, {
            action: 'Configuração Alterada',
            user,
            details: {
                '⚙️ Tipo': configType,
                '🔄 Novo Valor': newValue,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }

    static async logRateLimitHit(client, guildId, user, command) {
        await this.logAdmin(client, guildId, {
            action: 'Rate Limit Atingido',
            user,
            details: {
                '⚡ Comando': command,
                '🕐 Data': new Date().toLocaleString('pt-BR')
            }
        });
    }
}