import mongoose from 'mongoose';

/**
 * Schema para itens individuais de estoque
 * Cada documento representa uma unidade/conta separada do produto
 */
const stockItemSchema = new mongoose.Schema({
    // Referência ao produto
    productId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product',
        required: true,
        index: true
    },
    
    // Referência à loja
    storeId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Store',
        required: true,
        index: true
    },
    
    // Conteúdo da linha de estoque (ex: conta:senha, código, etc.)
    content: {
        type: String,
        required: true,
        trim: true,
        maxlength: 4000
    },
    
    // Status do item de estoque
    status: {
        type: String,
        enum: ['available', 'sold', 'reserved', 'expired'],
        default: 'available',
        index: true
    },
    
    // Informações de venda (quando vendido)
    soldTo: {
        type: String, // Discord ID do comprador
        default: null
    },
    soldAt: {
        type: Date,
        default: null
    },
    orderId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Order',
        default: null
    },
    
    // Informações de reserva (quando reservado)
    reservedTo: {
        type: String, // Discord ID do usuário que reservou
        default: null
    },
    reservedAt: {
        type: Date,
        default: null
    },
    reservationExpires: {
        type: Date,
        default: null
    },
    
    // Metadados
    createdBy: {
        type: String, // Discord ID do admin que criou
        required: true
    },
    lastModifiedBy: {
        type: String // Discord ID do admin que modificou
    },
    
    // Observações administrativas
    notes: {
        type: String,
        maxlength: 500,
        default: null
    }
}, {
    timestamps: true,
    collection: 'stock_items'
});

// Índices compostos para otimização
stockItemSchema.index({ productId: 1, status: 1 });
stockItemSchema.index({ storeId: 1, status: 1 });
stockItemSchema.index({ productId: 1, storeId: 1, status: 1 });
stockItemSchema.index({ soldTo: 1, soldAt: -1 });
stockItemSchema.index({ reservedTo: 1, reservationExpires: 1 });

// Middleware para limpar reservas expiradas
stockItemSchema.pre('find', function() {
    this.where({
        $or: [
            { status: { $ne: 'reserved' } },
            { reservationExpires: { $gte: new Date() } },
            { reservationExpires: null }
        ]
    });
});

// Métodos do schema
stockItemSchema.methods.markAsSold = function(buyerDiscordId, orderId = null) {
    this.status = 'sold';
    this.soldTo = buyerDiscordId;
    this.soldAt = new Date();
    this.orderId = orderId;
    
    // Limpa dados de reserva se existirem
    this.reservedTo = null;
    this.reservedAt = null;
    this.reservationExpires = null;
    
    return this.save();
};

stockItemSchema.methods.reserve = function(userDiscordId, durationMinutes = 15) {
    this.status = 'reserved';
    this.reservedTo = userDiscordId;
    this.reservedAt = new Date();
    this.reservationExpires = new Date(Date.now() + (durationMinutes * 60 * 1000));
    
    return this.save();
};

stockItemSchema.methods.releaseReservation = function() {
    this.status = 'available';
    this.reservedTo = null;
    this.reservedAt = null;
    this.reservationExpires = null;
    
    return this.save();
};

stockItemSchema.methods.markAsExpired = function() {
    this.status = 'expired';
    return this.save();
};

// Métodos estáticos
stockItemSchema.statics.findAvailableByProduct = function(productId) {
    return this.find({
        productId,
        status: 'available'
    }).sort({ createdAt: 1 }); // FIFO - primeiro a entrar, primeiro a sair
};

stockItemSchema.statics.findByStore = function(storeId, status = null) {
    const query = { storeId };
    if (status) {
        query.status = status;
    }
    return this.find(query).sort({ createdAt: -1 });
};

stockItemSchema.statics.countByProduct = function(productId, status = 'available') {
    return this.countDocuments({ productId, status });
};

stockItemSchema.statics.countByStore = function(storeId, status = 'available') {
    return this.countDocuments({ storeId, status });
};

stockItemSchema.statics.findExpiredReservations = function() {
    return this.find({
        status: 'reserved',
        reservationExpires: { $lt: new Date() }
    });
};

stockItemSchema.statics.cleanExpiredReservations = async function() {
    const expiredItems = await this.findExpiredReservations();
    
    for (const item of expiredItems) {
        await item.releaseReservation();
    }
    
    return expiredItems.length;
};

stockItemSchema.statics.getStockSummary = async function(productId) {
    const pipeline = [
        { $match: { productId: new mongoose.Types.ObjectId(productId) } },
        {
            $group: {
                _id: '$status',
                count: { $sum: 1 }
            }
        }
    ];
    
    const results = await this.aggregate(pipeline);
    
    const summary = {
        available: 0,
        sold: 0,
        reserved: 0,
        expired: 0,
        total: 0
    };
    
    results.forEach(result => {
        summary[result._id] = result.count;
        summary.total += result.count;
    });
    
    return summary;
};

export default mongoose.model('StockItem', stockItemSchema);
