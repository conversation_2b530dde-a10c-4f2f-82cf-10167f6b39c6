import { logger } from '../utils/logger.js';
import Product from '../models/Product.js';
import Store from '../models/Store.js';
import StockItem from '../models/StockItem.js';
import { StoreManager } from '../utils/storeManager.js';
import BotConfig from '../models/BotConfig.js';
import { BotLogger } from '../utils/botLogger.js';
import { EmbedBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } from 'discord.js';
import { COLORS, EMOJIS } from '../config/constants.js';

/**
 * Manipula interações de modais
 * @param {ModalSubmitInteraction} interaction 
 */
export async function handleModal(interaction) {
    const customId = interaction.customId;
    
    try {
        logger.info(`Modal enviado: ${customId} por ${interaction.user.tag}`);

        // Roteamento baseado no customId do modal
        // Para modais de edição, o customId tem formato: store_edit_storeId
        if (customId.startsWith('store_edit_')) {
            const storeId = customId.replace('store_edit_', '');
            await handleStoreEditModal(interaction, storeId);
            return;
        }

        // Para modais de criação de produto, o customId tem formato: product_create_storeId
        if (customId.startsWith('product_create_')) {
            const storeId = customId.replace('product_create_', '');
            await handleProductCreateModal(interaction, storeId);
            return;
        }

        // Para modais de criação de estoque, o customId tem formato: stock_create_storeId_productId
        if (customId.startsWith('stock_create_')) {
            const parts = customId.replace('stock_create_', '').split('_');
            const storeId = parts[0];
            const productId = parts[1];
            await handleStockCreateModal(interaction, storeId, productId);
            return;
        }

        const [category, action] = customId.split('_');

        switch (category) {
            case 'store':
                await handleStoreModal(interaction, action);
                break;
            case 'admin':
                await handleAdminModal(interaction, action);
                break;
            case 'config':
                await handleConfigModal(interaction);
                break;
            default:
                logger.warn(`Categoria de modal não reconhecida: ${category}`);
                await interaction.reply({
                    content: '❌ Modal não reconhecido.',
                    ephemeral: true
                });
        }

    } catch (error) {
        logger.error(`Erro ao processar modal ${customId}:`, error);
        
        const errorMessage = {
            content: '❌ Erro ao processar o formulário.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Manipula modais relacionados à loja
 */
async function handleStoreModal(interaction, action) {
    switch (action) {
        case 'create':
            await handleStoreCreateModal(interaction);
            break;
        case 'feedback':
            const feedback = interaction.fields.getTextInputValue('feedback_text');
            await interaction.reply({
                content: `📝 Obrigado pelo seu feedback: "${feedback}"`,
                ephemeral: true
            });
            break;
        case 'support':
            const issue = interaction.fields.getTextInputValue('issue_description');
            await interaction.reply({
                content: `🎫 Ticket de suporte criado! Descrição: "${issue}"`,
                ephemeral: true
            });
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de modal de loja não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula o modal de criação de loja
 */
async function handleStoreCreateModal(interaction) {
    try {
        // Verificação se o usuário é administrador
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem criar lojas.',
                ephemeral: true
            });
        }

        // Defer a resposta pois a criação pode demorar
        await interaction.deferReply({ ephemeral: true });

        // Extrai os dados do modal
        const banner = interaction.fields.getTextInputValue('store_banner');
        const name = interaction.fields.getTextInputValue('store_name');
        const color = interaction.fields.getTextInputValue('store_color');
        const description = interaction.fields.getTextInputValue('store_description');

        // Validações básicas
        if (!StoreManager.isValidUrl(banner)) {
            return await interaction.editReply({
                content: '❌ URL do banner inválida. Use uma URL válida de imagem (jpg, png, gif, webp).'
            });
        }

        if (!StoreManager.isValidColor(color)) {
            return await interaction.editReply({
                content: '❌ Cor inválida. Use formato hex (#FFFFFF) ou nome de cor (red, blue, etc).'
            });
        }

        // Cria a loja
        const result = await StoreManager.createStore(interaction, {
            banner,
            name,
            color,
            description
        });

        if (result.success) {
            await interaction.editReply({
                content: `✅ Loja **${name}** criada com sucesso!\n` +
                        `📍 Canal: ${result.channel}\n` +
                        `🆔 ID da Loja: \`${result.store._id}\``
            });
        }

    } catch (error) {
        logger.error('Erro ao processar modal de criação de loja:', error);

        const errorMessage = error.message || 'Erro interno do servidor';

        if (interaction.deferred) {
            await interaction.editReply({
                content: `❌ Erro ao criar loja: ${errorMessage}`
            });
        } else {
            await interaction.reply({
                content: `❌ Erro ao criar loja: ${errorMessage}`,
                ephemeral: true
            });
        }
    }
}

/**
 * Manipula modais administrativos
 */
async function handleAdminModal(interaction, action) {
    // Verificação básica de permissões
    if (!interaction.member.permissions.has('ADMINISTRATOR')) {
        return await interaction.reply({
            content: '❌ Você não tem permissão para usar este modal.',
            ephemeral: true
        });
    }

    switch (action) {
        case 'addproduct':
            const productName = interaction.fields.getTextInputValue('product_name');
            const productPrice = interaction.fields.getTextInputValue('product_price');
            
            await interaction.reply({
                content: `✅ Produto "${productName}" adicionado com preço R$ ${productPrice}`,
                ephemeral: true
            });
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de modal administrativo não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula o modal de edição de loja
 */
async function handleStoreEditModal(interaction, storeId) {
    try {
        // Verificação se o usuário é administrador
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem editar lojas.',
                ephemeral: true
            });
        }

        // Defer a resposta pois a edição pode demorar
        await interaction.deferReply({ ephemeral: true });

        // Busca a loja no banco de dados
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.editReply({
                content: '❌ Loja não encontrada ou inativa.'
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Esta loja não pertence a este servidor.'
            });
        }

        // Extrai os dados do modal
        const banner = interaction.fields.getTextInputValue('store_banner').trim();
        const name = interaction.fields.getTextInputValue('store_name').trim();
        const color = interaction.fields.getTextInputValue('store_color').trim();
        const description = interaction.fields.getTextInputValue('store_description').trim();

        // Objeto para armazenar apenas os campos que serão atualizados
        const updateData = {};
        let hasChanges = false;

        // Valida e adiciona banner se fornecido
        if (banner && banner !== store.banner) {
            if (!StoreManager.isValidUrl(banner)) {
                return await interaction.editReply({
                    content: '❌ URL do banner inválida. Use uma URL válida de imagem (jpg, png, gif, webp).'
                });
            }
            updateData.banner = banner;
            hasChanges = true;
        }

        // Valida e adiciona nome se fornecido
        if (name && name !== store.name) {
            // Verifica se já existe uma loja com o mesmo nome no servidor (exceto a atual)
            const existingStore = await Store.findOne({
                guildId: interaction.guild.id,
                name: name.toLowerCase(),
                isActive: true,
                _id: { $ne: storeId }
            });

            if (existingStore) {
                return await interaction.editReply({
                    content: '❌ Já existe uma loja com este nome no servidor.'
                });
            }

            updateData.name = name;
            hasChanges = true;
        }

        // Valida e adiciona cor se fornecida
        if (color && color !== store.color) {
            if (!StoreManager.isValidColor(color)) {
                return await interaction.editReply({
                    content: '❌ Cor inválida. Use formato hex (#FFFFFF) ou nome de cor (red, blue, etc).'
                });
            }
            updateData.color = color;
            hasChanges = true;
        }

        // Adiciona descrição se fornecida
        if (description && description !== store.description) {
            updateData.description = description;
            hasChanges = true;
        }

        // Verifica se há mudanças para aplicar
        if (!hasChanges) {
            return await interaction.editReply({
                content: '❌ Nenhuma alteração foi detectada. Os valores fornecidos são idênticos aos atuais.'
            });
        }

        // Adiciona metadados de modificação
        updateData.lastModifiedBy = interaction.user.id;

        // Atualiza a loja no banco de dados
        const updatedStore = await Store.findByIdAndUpdate(storeId, updateData, { new: true });

        // Atualiza o embed da loja no canal
        await StoreManager.updateStoreEmbed(updatedStore, interaction.guild);

        // Lista as alterações feitas
        const changes = [];
        if (updateData.banner) changes.push('🖼️ Banner');
        if (updateData.name) changes.push('📝 Nome');
        if (updateData.color) changes.push('🎨 Cor');
        if (updateData.description) changes.push('📄 Descrição');

        await interaction.editReply({
            content: `✅ Loja **${updatedStore.name}** editada com sucesso!\n\n` +
                    `**Alterações aplicadas:**\n${changes.join('\n')}\n\n` +
                    `📍 Canal: <#${updatedStore.channelId}>\n` +
                    `🆔 ID da Loja: \`${updatedStore._id}\``
        });

        logger.info(`Loja "${updatedStore.name}" editada por ${interaction.user.tag} em ${interaction.guild.name}. Alterações: ${changes.join(', ')}`);

    } catch (error) {
        logger.error('Erro ao editar loja:', error);

        const errorMessage = {
            content: '❌ Erro interno ao editar a loja. Tente novamente mais tarde.'
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(errorMessage);
        } else {
            await interaction.reply({ ...errorMessage, ephemeral: true });
        }
    }
}

/**
 * Manipula o modal de criação de produto
 */
async function handleProductCreateModal(interaction, storeId) {
    try {
        // Verificação se o usuário é administrador
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem criar produtos.',
                ephemeral: true
            });
        }

        // Defer a resposta pois a criação pode demorar
        await interaction.deferReply({ ephemeral: true });

        // Busca a loja no banco de dados
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.editReply({
                content: '❌ Loja não encontrada ou inativa.'
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Esta loja não pertence a este servidor.'
            });
        }

        // Extrai os dados do modal
        const name = interaction.fields.getTextInputValue('product_name');
        const priceInput = interaction.fields.getTextInputValue('product_price');
        const emoji = interaction.fields.getTextInputValue('product_emoji') || null;

        // Validação do preço
        const price = parseFloat(priceInput.replace(',', '.'));

        if (isNaN(price) || price <= 0) {
            return await interaction.editReply({
                content: '❌ Valor inválido. Digite um número positivo (ex: 29.99 ou 50).'
            });
        }

        // Validação do emoji (se fornecido)
        if (emoji && emoji.trim()) {
            const emojiRegex = /^(\p{Emoji}|<a?:\w+:\d+>)$/u;
            if (!emojiRegex.test(emoji.trim())) {
                return await interaction.editReply({
                    content: '❌ Emoji inválido. Use um emoji Unicode (🎮) ou um emoji customizado (<:nome:id>).'
                });
            }
        }

        // Cria o produto no banco de dados
        const productData = {
            name: name.trim(),
            description: `Produto da loja ${store.name}`, // Descrição padrão
            price: price,
            stock: 0, // Produto criado sem estoque
            category: 'digital', // Categoria padrão
            status: 'out_of_stock', // Status inicial sem estoque
            createdBy: interaction.user.id,
            emoji: emoji ? emoji.trim() : null,
            storeId: storeId // Adiciona referência à loja
        };

        const product = new Product(productData);
        await product.save();

        // Mensagem de sucesso
        const successMessage = `✅ Produto criado com sucesso!\n\n` +
                              `**📦 Nome:** ${product.name}\n` +
                              `**💰 Valor:** R$ ${product.price.toFixed(2).replace('.', ',')}\n` +
                              `**${product.emoji || '📦'} Emoji:** ${product.emoji || 'Nenhum'}\n` +
                              `**🏪 Loja:** ${store.name}\n` +
                              `**📊 Status:** Sem estoque (use comandos de estoque para adicionar)\n\n` +
                              `🆔 **ID do Produto:** \`${product._id}\``;

        await interaction.editReply({
            content: successMessage
        });

        logger.info(`Produto "${product.name}" criado por ${interaction.user.tag} na loja "${store.name}" (${interaction.guild.name})`);
        
        // Log da criação do produto
        await BotLogger.logProductCreated(interaction.guild, interaction.user, product, store);

    } catch (error) {
        logger.error('Erro ao criar produto:', error);

        const errorMessage = {
            content: '❌ Erro interno ao criar o produto. Tente novamente mais tarde.'
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(errorMessage);
        } else {
            await interaction.reply({ ...errorMessage, ephemeral: true });
        }
    }
}

/**
 * Manipula modal de criação de estoque
 */
async function handleStockCreateModal(interaction, storeId, productId) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        // Busca o produto e a loja
        const product = await Product.findById(productId);
        const store = await Store.findById(storeId);

        if (!product || !store) {
            return await interaction.editReply({
                content: '❌ Produto ou loja não encontrados.'
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.editReply({
                content: '❌ Esta loja não pertence a este servidor.'
            });
        }

        // Obtém os dados do modal
        const stockLines = interaction.fields.getTextInputValue('stock_lines');
        const notes = interaction.fields.getTextInputValue('stock_notes') || null;

        // Log para debug
        logger.info(`Processando criação de estoque: ${stockLines.length} caracteres, ${stockLines.split('\n').length} linhas`);
        logger.debug(`Conteúdo do estoque: ${stockLines.substring(0, 200)}...`);

        // Validação básica do comprimento
        if (stockLines.length > VALIDATION.STOCK_CONTENT.MAX_TOTAL_LENGTH) {
            return await interaction.editReply({
                content: `❌ O texto de estoque é muito longo. Máximo permitido: ${VALIDATION.STOCK_CONTENT.MAX_TOTAL_LENGTH} caracteres.`
            });
        }

        // Processa as linhas de estoque
        const lines = stockLines.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        if (lines.length === 0) {
            return await interaction.editReply({
                content: '❌ Nenhuma linha de estoque válida foi fornecida.'
            });
        }

        // Validação adicional: verifica se alguma linha é muito longa
        const longLines = lines.filter(line => line.length > VALIDATION.STOCK_CONTENT.MAX_LINE_LENGTH);
        if (longLines.length > 0) {
            return await interaction.editReply({
                content: `❌ Algumas linhas de estoque são muito longas (máximo ${VALIDATION.STOCK_CONTENT.MAX_LINE_LENGTH} caracteres por linha). Verifique e tente novamente.`
            });
        }

        // Cria os itens de estoque
        const stockItems = [];
        const createdBy = interaction.user.id;

        for (const line of lines) {
            const stockItem = new StockItem({
                productId: productId,
                storeId: storeId,
                content: line,
                status: 'available',
                createdBy: createdBy,
                notes: notes
            });

            stockItems.push(stockItem);
        }

        // Salva todos os itens de estoque
        await StockItem.insertMany(stockItems);

        // Atualiza o contador de estoque do produto
        const newStockCount = await StockItem.countByProduct(productId, 'available');

        // Atualiza o produto com o novo estoque e status
        product.stock = newStockCount;
        if (newStockCount > 0 && product.status === 'out_of_stock') {
            product.status = 'active';
        }
        product.lastModifiedBy = createdBy;
        await product.save();

        // Mensagem de sucesso
        const successMessage = `✅ **Estoque adicionado com sucesso!**\n\n` +
                              `📦 **Produto:** ${product.name}\n` +
                              `🏪 **Loja:** ${store.name}\n` +
                              `📊 **Itens adicionados:** ${stockItems.length}\n` +
                              `📈 **Estoque total:** ${newStockCount}\n` +
                              `🔄 **Status:** ${product.status === 'active' ? '✅ Ativo' : '⚠️ ' + product.status}`;

        await interaction.editReply({
            content: successMessage
        });

        logger.info(`${stockItems.length} itens de estoque adicionados ao produto "${product.name}" por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao criar estoque:', error);

        const errorMessage = {
            content: '❌ Erro interno ao adicionar estoque. Tente novamente mais tarde.'
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(errorMessage);
        } else {
            await interaction.reply({ ...errorMessage, ephemeral: true });
        }
    }
}

/**
 * Manipula modais de configuração do bot
 * @param {import('discord.js').ModalSubmitInteraction} interaction 
 */
async function handleConfigModal(interaction) {
    try {
        const customId = interaction.customId;
        
        // Verificação de permissão
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem usar esta configuração.',
                ephemeral: true
            });
        }

        if (customId === 'config_mercadopago_modal') {
            await handleMercadoPagoModal(interaction);
        } else if (customId === 'config_rate_limit_modal') {
            await handleRateLimitModal(interaction);
        } else {
            logger.warn(`Modal de configuração não reconhecido: ${customId}`);
            await interaction.reply({
                content: '❌ Modal de configuração não reconhecido.',
                ephemeral: true
            });
        }
    } catch (error) {
        logger.error('Erro no handler de modal de configuração:', error);
        
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
                content: '❌ Ocorreu um erro ao processar a configuração.',
                ephemeral: true
            });
        }
    }
}

/**
 * Processa modal de configuração do MercadoPago
 */
async function handleMercadoPagoModal(interaction) {
    const accessToken = interaction.fields.getTextInputValue('mp_access_token');
    const publicKey = interaction.fields.getTextInputValue('mp_public_key');
    const webhookSecret = interaction.fields.getTextInputValue('mp_webhook_secret') || null;

    // Validações básicas
    if (!accessToken.startsWith('APP_USR-') || !publicKey.startsWith('APP_USR-')) {
        return await interaction.reply({
            content: '❌ Tokens inválidos. Verifique se começam com "APP_USR-".',
            ephemeral: true
        });
    }

    try {
        // Busca ou cria configuração
        let config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config) {
            config = new BotConfig({ guildId: interaction.guild.id });
        }

        // Atualiza configurações do MercadoPago
        await config.updateMercadoPago({
            accessToken,
            publicKey,
            webhookSecret,
            isEnabled: true
        }, interaction.user.id);

        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${EMOJIS.SUCCESS} MercadoPago Configurado`)
            .setDescription('As configurações do MercadoPago foram salvas com sucesso!')
            .addFields(
                {
                    name: '🔑 Access Token',
                    value: `\`${accessToken.substring(0, 20)}...\``,
                    inline: true
                },
                {
                    name: '🔓 Public Key',
                    value: `\`${publicKey.substring(0, 20)}...\``,
                    inline: true
                },
                {
                    name: '🔗 Webhook',
                    value: webhookSecret ? '✅ Configurado' : '❌ Não configurado',
                    inline: true
                }
            )
            .setTimestamp();

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });

        // Log da configuração
        await BotLogger.logConfigChange(
            interaction.guild,
            interaction.user,
            'MercadoPago',
            'Configuração da API do MercadoPago atualizada'
        );

    } catch (error) {
        logger.error('Erro ao salvar configuração do MercadoPago:', error);
        
        const errorEmbed = new EmbedBuilder()
            .setColor(COLORS.ERROR)
            .setTitle(`${EMOJIS.ERROR} Erro`)
            .setDescription('Ocorreu um erro ao salvar as configurações do MercadoPago.')
            .setTimestamp();

        await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true
        });
    }
}

/**
 * Processa modal de configuração de Rate Limiting
 */
async function handleRateLimitModal(interaction) {
    const maxRequestsStr = interaction.fields.getTextInputValue('rl_max_requests');
    const windowMsStr = interaction.fields.getTextInputValue('rl_window_ms');
    const enabledStr = interaction.fields.getTextInputValue('rl_enabled').toLowerCase();

    // Validações
    const maxRequests = parseInt(maxRequestsStr);
    const windowMs = parseInt(windowMsStr);
    const isEnabled = enabledStr === 'true' || enabledStr === '1' || enabledStr === 'sim';

    if (isNaN(maxRequests) || maxRequests < 1 || maxRequests > 100) {
        return await interaction.reply({
            content: '❌ Número máximo de requisições deve ser entre 1 e 100.',
            ephemeral: true
        });
    }

    if (isNaN(windowMs) || windowMs < 1000 || windowMs > 3600000) {
        return await interaction.reply({
            content: '❌ Janela de tempo deve ser entre 1000ms (1s) e 3600000ms (1h).',
            ephemeral: true
        });
    }

    try {
        // Busca ou cria configuração
        let config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config) {
            config = new BotConfig({ guildId: interaction.guild.id });
        }

        // Atualiza configurações de rate limiting
        await config.updateRateLimiting({
            windowMs,
            maxRequests,
            isEnabled
        }, interaction.user.id);

        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${EMOJIS.SUCCESS} Rate Limiting Configurado`)
            .setDescription('As configurações de rate limiting foram salvas com sucesso!')
            .addFields(
                {
                    name: '⚡ Status',
                    value: isEnabled ? '✅ Ativado' : '❌ Desativado',
                    inline: true
                },
                {
                    name: '📊 Limite',
                    value: `${maxRequests} requisições`,
                    inline: true
                },
                {
                    name: '⏱️ Janela',
                    value: `${windowMs / 1000}s`,
                    inline: true
                }
            )
            .setTimestamp();

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });

        // Log da configuração
        await BotLogger.logRateLimitChange(
            interaction.guild,
            interaction.user,
            {
                windowMs: config.rateLimiting.windowMs,
                maxRequests: config.rateLimiting.maxRequests,
                isEnabled: config.rateLimiting.isEnabled
            }
        );

    } catch (error) {
        logger.error('Erro ao salvar configuração de rate limiting:', error);
        
        const errorEmbed = new EmbedBuilder()
            .setColor(COLORS.ERROR)
            .setTitle(`${EMOJIS.ERROR} Erro`)
            .setDescription('Ocorreu um erro ao salvar as configurações de rate limiting.')
            .setTimestamp();

        await interaction.reply({
            embeds: [errorEmbed],
            ephemeral: true
        });
    }
}
