import { Events } from 'discord.js';
import { logger } from '../utils/logger.js';

export default {
    name: Events.ClientReady,
    once: true,
    async execute(client) {
        try {
            logger.info(`✅ Bot logado como ${client.user.tag}`);
            logger.info(`🔗 Conectado a ${client.guilds.cache.size} servidor(es)`);
            logger.info(`👥 Servindo ${client.users.cache.size} usuários`);
            
            // Define a atividade do bot
            client.user.setActivity('Gerenciando a loja', { type: 'WATCHING' });
            
            // Log de comandos disponíveis
            const commandCount = client.commands.size;
            logger.info(`📋 ${commandCount} comandos disponíveis`);
            
            if (process.env.NODE_ENV === 'development') {
                logger.debug('🔧 Bot rodando em modo de desenvolvimento');
            }
            
        } catch (error) {
            logger.error('❌ Erro no evento ready:', error);
        }
    }
};
