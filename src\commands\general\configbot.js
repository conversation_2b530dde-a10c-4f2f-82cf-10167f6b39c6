import { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';
import { BOT_CONFIG, COLORS, EMOJIS } from '../../config/constants.js';
import BotConfig from '../../models/BotConfig.js';
import { logger } from '../../utils/logger.js';

export default {
    data: new SlashCommandBuilder()
        .setName('configbot')
        .setDescription('Configura as opções do bot (apenas administradores)'),
    
    async execute(interaction) {
        try {
            // Verificação se o usuário é administrador
            if (!interaction.member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem executar este comando.',
                    ephemeral: true
                });
            }

            // Busca configuração atual do servidor
            const config = await BotConfig.findByGuild(interaction.guild.id);
            
            // Cria embed com configurações atuais
            const embed = new EmbedBuilder()
                .setColor(COLORS.PRIMARY)
                .setTitle(`${EMOJIS.INFO} Configurações do Bot`)
                .setDescription('Configure as opções do bot para este servidor')
                .setThumbnail(interaction.client.user.displayAvatarURL())
                .addFields(
                    {
                        name: '📋 Canal de Logs Admin',
                        value: config?.adminLogChannelId 
                            ? `<#${config.adminLogChannelId}>` 
                            : '`Não configurado`',
                        inline: true
                    },
                    {
                        name: '📢 Canal de Logs Públicas',
                        value: config?.publicLogChannelId 
                            ? `<#${config.publicLogChannelId}>` 
                            : '`Não configurado`',
                        inline: true
                    },
                    {
                        name: '💳 MercadoPago',
                        value: config?.mercadoPago?.isEnabled 
                            ? `${EMOJIS.SUCCESS} Configurado` 
                            : '`Não configurado`',
                        inline: true
                    },
                    {
                        name: '⚡ Rate Limiting',
                        value: config?.rateLimiting?.isEnabled !== false
                            ? `${EMOJIS.SUCCESS} Ativo (${config?.rateLimiting?.maxRequests || 10} req/min)`
                            : '`Desativado`',
                        inline: true
                    },
                    {
                        name: '🕐 Última Modificação',
                        value: config?.updatedAt 
                            ? `<t:${Math.floor(config.updatedAt.getTime() / 1000)}:R>`
                            : '`Nunca`',
                        inline: true
                    },
                    {
                        name: '👤 Modificado por',
                        value: config?.lastModifiedBy 
                            ? `<@${config.lastModifiedBy}>`
                            : '`Sistema`',
                        inline: true
                    }
                )
                .setFooter({ 
                    text: `Servidor: ${interaction.guild.name}`,
                    iconURL: interaction.guild.iconURL()
                })
                .setTimestamp();

            // Cria botões de configuração
            const row1 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('config_admin_logs')
                        .setLabel('Canal Admin')
                        .setEmoji('📋')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('config_public_logs')
                        .setLabel('Canal Público')
                        .setEmoji('📢')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('config_mercadopago')
                        .setLabel('MercadoPago')
                        .setEmoji('💳')
                        .setStyle(ButtonStyle.Success)
                );

            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('config_rate_limit')
                        .setLabel('Rate Limiting')
                        .setEmoji('⚡')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('config_view_logs')
                        .setLabel('Ver Logs')
                        .setEmoji('📊')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('config_reset_all')
                        .setLabel('Resetar Tudo')
                        .setEmoji('🔄')
                        .setStyle(ButtonStyle.Danger)
                );

            await interaction.reply({
                embeds: [embed],
                components: [row1, row2],
                ephemeral: true
            });

            logger.info(`Comando configbot executado por ${interaction.user.tag} em ${interaction.guild.name}`);

        } catch (error) {
            logger.error('Erro no comando configbot:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor(COLORS.ERROR)
                .setTitle(`${EMOJIS.ERROR} Erro`)
                .setDescription('Ocorreu um erro ao carregar as configurações do bot.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};