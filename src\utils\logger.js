/**
 * Sistema de logging simples e eficiente
 */

const LOG_LEVELS = {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3
};

const COLORS = {
    ERROR: '\x1b[31m', // Vermelho
    WARN: '\x1b[33m',  // Amarelo
    INFO: '\x1b[36m',  // Ciano
    DEBUG: '\x1b[35m', // Magenta
    RESET: '\x1b[0m'   // Reset
};

class Logger {
    constructor() {
        this.level = this.getLogLevel();
    }

    getLogLevel() {
        const envLevel = process.env.LOG_LEVEL?.toUpperCase() || 'INFO';
        return LOG_LEVELS[envLevel] !== undefined ? LOG_LEVELS[envLevel] : LOG_LEVELS.INFO;
    }

    formatMessage(level, message, ...args) {
        const timestamp = new Date().toISOString();
        const color = COLORS[level];
        const reset = COLORS.RESET;
        
        const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ') : '';

        return `${color}[${timestamp}] [${level}]${reset} ${message}${formattedArgs}`;
    }

    log(level, message, ...args) {
        if (LOG_LEVELS[level] <= this.level) {
            console.log(this.formatMessage(level, message, ...args));
        }
    }

    error(message, ...args) {
        this.log('ERROR', message, ...args);
    }

    warn(message, ...args) {
        this.log('WARN', message, ...args);
    }

    info(message, ...args) {
        this.log('INFO', message, ...args);
    }

    debug(message, ...args) {
        this.log('DEBUG', message, ...args);
    }
}

export const logger = new Logger();
