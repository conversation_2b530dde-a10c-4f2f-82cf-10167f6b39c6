import mongoose from 'mongoose';

const productSchema = new mongoose.Schema({
    // Informações básicas do produto
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    description: {
        type: String,
        required: true,
        maxlength: 1000
    },
    
    // Preço e estoque
    price: {
        type: Number,
        required: true,
        min: 0
    },
    originalPrice: {
        type: Number,
        min: 0
    },
    stock: {
        type: Number,
        required: true,
        min: 0,
        default: 0
    },
    
    // Categorização
    category: {
        type: String,
        required: true,
        enum: ['digital', 'physical', 'service', 'subscription']
    },
    tags: [{
        type: String,
        trim: true
    }],
    
    // Mídia
    images: [{
        url: String,
        alt: String,
        isPrimary: {
            type: Boolean,
            default: false
        }
    }],
    
    // Status e disponibilidade
    status: {
        type: String,
        enum: ['active', 'inactive', 'out_of_stock', 'discontinued'],
        default: 'active'
    },
    isDigital: {
        type: Boolean,
        default: false
    },
    
    // Configurações de venda
    maxQuantityPerUser: {
        type: Number,
        default: null // null = sem limite
    },
    requiresShipping: {
        type: Boolean,
        default: true
    },
    
    // Estatísticas
    totalSold: {
        type: Number,
        default: 0,
        min: 0
    },
    views: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // Referência à loja
    storeId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Store',
        required: true
    },

    // Emoji do produto (opcional)
    emoji: {
        type: String,
        maxlength: 50,
        default: null
    },

    // Metadados
    createdBy: {
        type: String, // Discord ID do admin que criou
        required: true
    },
    lastModifiedBy: {
        type: String // Discord ID do admin que modificou
    }
}, {
    timestamps: true,
    collection: 'products'
});

// Índices para otimização
productSchema.index({ name: 'text', description: 'text' });
productSchema.index({ category: 1, status: 1 });
productSchema.index({ price: 1 });
productSchema.index({ createdAt: -1 });
productSchema.index({ totalSold: -1 });
productSchema.index({ storeId: 1, status: 1 }); // Índice para consultas por loja

// Virtual para verificar se está em promoção
productSchema.virtual('isOnSale').get(function() {
    return this.originalPrice && this.originalPrice > this.price;
});

// Virtual para calcular desconto
productSchema.virtual('discountPercentage').get(function() {
    if (this.isOnSale) {
        return Math.round(((this.originalPrice - this.price) / this.originalPrice) * 100);
    }
    return 0;
});

// Métodos do schema
productSchema.methods.updateStock = function(quantity) {
    this.stock += quantity;
    if (this.stock < 0) this.stock = 0;
    
    // Atualiza status baseado no estoque
    if (this.stock === 0 && this.status === 'active') {
        this.status = 'out_of_stock';
    } else if (this.stock > 0 && this.status === 'out_of_stock') {
        this.status = 'active';
    }
    
    return this.save();
};

productSchema.methods.incrementViews = function() {
    this.views += 1;
    return this.save();
};

productSchema.methods.recordSale = function(quantity = 1) {
    this.totalSold += quantity;
    this.stock -= quantity;
    
    if (this.stock <= 0) {
        this.status = 'out_of_stock';
    }
    
    return this.save();
};

// Métodos estáticos
productSchema.statics.findAvailable = function() {
    return this.find({ 
        status: 'active',
        stock: { $gt: 0 }
    });
};

productSchema.statics.findByCategory = function(category) {
    return this.find({ category, status: 'active' });
};

productSchema.statics.searchProducts = function(query) {
    return this.find({
        $text: { $search: query },
        status: 'active'
    }).sort({ score: { $meta: 'textScore' } });
};

productSchema.statics.findByStore = function(storeId, includeOutOfStock = false) {
    const query = { storeId };

    if (!includeOutOfStock) {
        query.status = { $ne: 'out_of_stock' };
    }

    return this.find(query).sort({ createdAt: -1 });
};

productSchema.statics.findActiveByStore = function(storeId) {
    return this.find({
        storeId,
        status: 'active',
        stock: { $gt: 0 }
    }).sort({ createdAt: -1 });
};

export default mongoose.model('Product', productSchema);
